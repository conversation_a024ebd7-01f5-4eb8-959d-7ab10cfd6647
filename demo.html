<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大宗交易平台 UI 原型演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .demo-container {
            text-align: center;
            max-width: 800px;
            padding: 40px;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 36px;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .subtitle {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .feature-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .feature p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .demo-button {
            display: inline-block;
            padding: 15px 30px;
            background: white;
            color: #667eea;
            text-decoration: none;
            border-radius: 25px;
            font-size: 18px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }
        
        .info {
            margin-top: 40px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .info h3 {
            margin-bottom: 15px;
        }
        
        .info ul {
            text-align: left;
            list-style: none;
            padding: 0;
        }
        
        .info li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .info li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #4CAF50;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 20px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="demo-container">
        <div class="logo">
            <i class="fas fa-cube"></i>
        </div>
        
        <h1>大宗交易平台 UI 原型</h1>
        <p class="subtitle">基于需求文档设计的完整用户界面原型</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <h3>货源管理</h3>
                <p>完整的货源发布、管理和展示功能</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-handshake"></i>
                </div>
                <h3>交易流程</h3>
                <p>从需求提交到合同签署的完整流程</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <h3>角色工作台</h3>
                <p>谈判人、商务、验货人专用界面</p>
            </div>
            
            <div class="feature">
                <div class="feature-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3>验货管理</h3>
                <p>专业的验货流程和报告系统</p>
            </div>
        </div>
        
        <a href="index.html" class="demo-button">
            <i class="fas fa-play"></i> 开始体验
        </a>
        
        <div class="info">
            <h3>功能特性</h3>
            <ul>
                <li>响应式设计，支持桌面端和移动端</li>
                <li>完整的业务流程覆盖</li>
                <li>现代化的用户界面设计</li>
                <li>丰富的交互动画效果</li>
                <li>模拟真实的数据和操作</li>
                <li>无需后端支持，纯前端演示</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>使用说明</h3>
            <ul>
                <li>点击"开始体验"进入主界面</li>
                <li>使用顶部导航和侧边栏切换页面</li>
                <li>尝试发布货源和提交需求</li>
                <li>体验不同角色的工作台功能</li>
                <li>查看订单管理和会员中心</li>
                <li>所有操作都是模拟的，数据不会保存</li>
            </ul>
        </div>
    </div>
</body>
</html>
