// 页面切换功能
function showPage(pageId) {
    // 隐藏所有页面
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => {
        page.classList.remove('active');
    });

    // 显示指定页面
    const targetPage = document.getElementById(pageId);
    if (targetPage) {
        targetPage.classList.add('active');
    }

    // 更新侧边栏菜单状态
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
        item.classList.remove('active');
    });

    // 根据页面ID设置对应菜单项为活跃状态
    const menuMap = {
        'dashboard': 0,
        'goods-management': 1,
        'trade-management': 2,
        'order-center': 3,
        'role-workspace': 4,
        'verification': 5
    };

    if (menuMap[pageId] !== undefined) {
        menuItems[menuMap[pageId]].classList.add('active');
    }

    // 更新顶部导航状态
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });

    // 根据页面设置导航状态
    if (pageId === 'goods-market') {
        navLinks[1].classList.add('active');
    } else if (pageId === 'trade-center' || pageId === 'trade-management') {
        navLinks[2].classList.add('active');
    } else if (pageId === 'member-center') {
        navLinks[3].classList.add('active');
    } else {
        navLinks[0].classList.add('active');
    }
}

// 角色工作台切换
function showRoleTab(roleId) {
    // 隐藏所有角色内容
    const roleContents = document.querySelectorAll('.role-content');
    roleContents.forEach(content => {
        content.classList.remove('active');
    });

    // 显示指定角色内容
    const targetContent = document.getElementById(roleId + '-workspace');
    if (targetContent) {
        targetContent.classList.add('active');
    }

    // 更新角色标签状态
    const roleTabs = document.querySelectorAll('.role-tab');
    roleTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    // 设置当前标签为活跃状态
    event.target.classList.add('active');
}

// 订单筛选功能
function filterOrders(status) {
    const filterTabs = document.querySelectorAll('.filter-tab');
    filterTabs.forEach(tab => {
        tab.classList.remove('active');
    });

    event.target.classList.add('active');

    // 这里可以添加实际的筛选逻辑
    console.log('筛选订单状态:', status);
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 默认显示工作台
    showPage('dashboard');

    // 添加菜单项点击事件
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const pageIds = ['dashboard', 'goods-management', 'trade-management', 'order-center', 'role-workspace', 'verification'];
            if (pageIds[index]) {
                showPage(pageIds[index]);
            }
        });
    });

    // 添加订单筛选标签点击事件
    const filterTabs = document.querySelectorAll('.filter-tab');
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            filterOrders(this.textContent);
        });
    });

    // 添加文件上传事件
    const uploadAreas = document.querySelectorAll('.upload-area');
    uploadAreas.forEach(area => {
        area.addEventListener('click', function() {
            const fileInput = this.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.click();
            }
        });

        area.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.style.borderColor = '#667eea';
            this.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
        });

        area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = 'transparent';
        });

        area.addEventListener('drop', function(e) {
            e.preventDefault();
            this.style.borderColor = '#ddd';
            this.style.backgroundColor = 'transparent';

            const files = e.dataTransfer.files;
            handleFileUpload({ target: { files: files } });
        });
    });

    // 添加表单提交事件
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmit(this);
        });
    });

    // 初始化货源市场表格功能
    initMarketTable();

    // 添加动画效果
    addAnimations();

    // 模拟数据加载
    loadMockData();
});

// 添加动画效果
function addAnimations() {
    // 统计卡片动画
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        }, index * 100);
    });
    
    // 快捷操作卡片动画
    const actionCards = document.querySelectorAll('.action-card');
    actionCards.forEach((card, index) => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// 模拟数据加载
function loadMockData() {
    // 模拟实时数据更新
    setInterval(() => {
        updateStats();
    }, 5000);
}

// 更新统计数据
function updateStats() {
    const statValues = document.querySelectorAll('.stat-info h3');
    statValues.forEach(stat => {
        if (stat.textContent.includes('¥')) {
            // 更新金额
            const currentValue = parseInt(stat.textContent.replace(/[¥,]/g, ''));
            const newValue = currentValue + Math.floor(Math.random() * 1000);
            stat.textContent = `¥${newValue.toLocaleString()}`;
        } else {
            // 更新数字
            const currentValue = parseInt(stat.textContent);
            const change = Math.floor(Math.random() * 5) - 2; // -2 到 2 的随机变化
            const newValue = Math.max(0, currentValue + change);
            stat.textContent = newValue.toString();
        }
    });
}

// 货源发布功能
function publishGoods() {
    alert('跳转到货源发布页面');
    // 这里可以添加实际的页面跳转逻辑
}

// 提交需求功能
function submitDemand() {
    alert('跳转到需求提交页面');
    // 这里可以添加实际的页面跳转逻辑
}

// 搜索功能
function searchGoods() {
    const searchInput = document.querySelector('.filter-bar input[type="text"]');
    const searchTerm = searchInput.value.trim();
    
    if (searchTerm) {
        alert(`搜索货源: ${searchTerm}`);
        // 这里可以添加实际的搜索逻辑
    }
}

// 筛选功能
function filterGoods() {
    const statusSelect = document.querySelector('.filter-bar select:first-of-type');
    const typeSelect = document.querySelector('.filter-bar select:last-of-type');
    
    const status = statusSelect.value;
    const type = typeSelect.value;
    
    console.log(`筛选条件 - 状态: ${status}, 类型: ${type}`);
    // 这里可以添加实际的筛选逻辑
}

// 查看货源详情
function viewGoodsDetail(goodsId) {
    alert(`查看货源详情: ${goodsId}`);
    // 这里可以添加实际的详情页面逻辑
}

// 编辑货源
function editGoods(goodsId) {
    alert(`编辑货源: ${goodsId}`);
    // 这里可以添加实际的编辑逻辑
}

// 通知功能
function showNotifications() {
    const notifications = [
        { type: 'info', message: '您有新的货源询盘', time: '刚刚' },
        { type: 'success', message: '订单支付成功', time: '5分钟前' },
        { type: 'warning', message: '货源验货即将到期', time: '1小时前' }
    ];
    
    // 这里可以显示通知列表
    console.log('通知列表:', notifications);
}

// 用户菜单功能
function toggleUserMenu() {
    // 这里可以添加用户菜单的显示/隐藏逻辑
    const userMenu = document.createElement('div');
    userMenu.className = 'user-menu';
    userMenu.innerHTML = `
        <div class="menu-item">个人资料</div>
        <div class="menu-item">账户设置</div>
        <div class="menu-item">退出登录</div>
    `;
    
    // 添加到页面并显示
    document.body.appendChild(userMenu);
}

// 响应式菜单切换
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');
    
    if (window.innerWidth <= 768) {
        sidebar.style.transform = sidebar.style.transform === 'translateX(0px)' ? 'translateX(-100%)' : 'translateX(0px)';
    }
}

// 窗口大小改变时的处理
window.addEventListener('resize', function() {
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');
    
    if (window.innerWidth > 768) {
        sidebar.style.transform = 'translateX(0px)';
        content.style.marginLeft = '250px';
    } else {
        content.style.marginLeft = '0';
    }
});

// 表单验证功能
function validateForm(formData) {
    const errors = [];
    
    // 基本验证规则
    if (!formData.name || formData.name.trim() === '') {
        errors.push('名称不能为空');
    }
    
    if (!formData.quantity || formData.quantity <= 0) {
        errors.push('数量必须大于0');
    }
    
    if (!formData.price || formData.price <= 0) {
        errors.push('价格必须大于0');
    }
    
    return errors;
}

// 文件上传功能
function handleFileUpload(event) {
    const files = event.target.files;
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    
    for (let file of files) {
        if (file.size > maxSize) {
            alert('文件大小不能超过5MB');
            return false;
        }
        
        if (!allowedTypes.includes(file.type)) {
            alert('只支持JPG、PNG、GIF格式的图片');
            return false;
        }
    }
    
    // 处理文件上传
    console.log('上传文件:', files);
    return true;
}

// 数据导出功能
function exportData(type) {
    const data = {
        goods: '货源数据',
        orders: '订单数据',
        transactions: '交易数据'
    };
    
    console.log(`导出${data[type]}`);
    alert(`正在导出${data[type]}...`);
}

// 打印功能
function printPage() {
    window.print();
}

// 全屏功能
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        document.exitFullscreen();
    }
}

// 表单提交处理
function handleFormSubmit(form) {
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 表单验证
    const errors = validateForm(data);
    if (errors.length > 0) {
        alert('表单验证失败：\n' + errors.join('\n'));
        return false;
    }

    // 模拟提交
    console.log('提交表单数据:', data);
    alert('表单提交成功！');

    // 重置表单
    form.reset();

    return true;
}

// 货源询价功能
function inquirePrice(goodsId) {
    const modal = createModal('询价', `
        <form id="inquire-form">
            <div class="form-group">
                <label>需求数量：</label>
                <input type="number" name="quantity" class="form-input" placeholder="请输入需求数量" required>
            </div>
            <div class="form-group">
                <label>预期价格：</label>
                <input type="number" name="price" class="form-input" placeholder="请输入预期价格">
            </div>
            <div class="form-group">
                <label>联系方式：</label>
                <input type="text" name="contact" class="form-input" placeholder="请输入联系方式" required>
            </div>
            <div class="form-group">
                <label>备注：</label>
                <textarea name="note" class="form-textarea" placeholder="请输入备注信息"></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-primary">提交询价</button>
            </div>
        </form>
    `);

    document.body.appendChild(modal);

    // 添加表单提交事件
    const inquireForm = document.getElementById('inquire-form');
    inquireForm.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('询价信息已提交！');
        closeModal();
    });
}

// 创建模态框
function createModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    // 添加模态框样式
    if (!document.getElementById('modal-styles')) {
        const style = document.createElement('style');
        style.id = 'modal-styles';
        style.textContent = `
            .modal-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            }
            .modal-content {
                background: white;
                border-radius: 10px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            }
            .modal-header {
                padding: 20px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .modal-header h3 {
                margin: 0;
                color: #2c3e50;
            }
            .modal-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #7f8c8d;
            }
            .modal-body {
                padding: 20px;
            }
        `;
        document.head.appendChild(style);
    }

    return modal;
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// 验货功能
function startVerification(goodsId) {
    const modal = createModal('开始验货', `
        <form id="verification-form">
            <div class="form-group">
                <label>验货地点：</label>
                <input type="text" name="location" class="form-input" placeholder="请输入验货地点" required>
            </div>
            <div class="form-group">
                <label>验货时间：</label>
                <input type="datetime-local" name="time" class="form-input" required>
            </div>
            <div class="form-group">
                <label>验货图片：</label>
                <div class="upload-area">
                    <i class="fas fa-camera"></i>
                    <p>上传验货图片</p>
                    <input type="file" multiple accept="image/*" style="display: none;">
                </div>
            </div>
            <div class="form-group">
                <label>验货报告：</label>
                <textarea name="report" class="form-textarea" placeholder="请输入验货报告" required></textarea>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-primary">提交验货</button>
            </div>
        </form>
    `);

    document.body.appendChild(modal);

    // 添加表单提交事件
    const verificationForm = document.getElementById('verification-form');
    verificationForm.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('验货报告已提交！');
        closeModal();
    });
}

// 合同生成功能
function generateContract(tradeId) {
    showPage('contract-generator');
    // 这里可以添加合同生成的具体逻辑
}

// 物流上传功能
function uploadLogistics(orderId) {
    const modal = createModal('上传物流信息', `
        <form id="logistics-form">
            <div class="form-group">
                <label>物流公司：</label>
                <input type="text" name="company" class="form-input" placeholder="请输入物流公司" required>
            </div>
            <div class="form-group">
                <label>运单号：</label>
                <input type="text" name="tracking" class="form-input" placeholder="请输入运单号" required>
            </div>
            <div class="form-group">
                <label>发货时间：</label>
                <input type="datetime-local" name="shipTime" class="form-input" required>
            </div>
            <div class="form-group">
                <label>预计到达：</label>
                <input type="datetime-local" name="arrivalTime" class="form-input" required>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-primary">提交物流</button>
            </div>
        </form>
    `);

    document.body.appendChild(modal);

    // 添加表单提交事件
    const logisticsForm = document.getElementById('logistics-form');
    logisticsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('物流信息已提交！');
        closeModal();
    });
}

// 初始化货源市场表格功能
function initMarketTable() {
    // 全选复选框功能
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const rowCheckboxes = document.querySelectorAll('.row-checkbox');
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // 行复选框功能
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.row-checkbox');
            const checkedCheckboxes = document.querySelectorAll('.row-checkbox:checked');
            const selectAllCheckbox = document.getElementById('selectAll');

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
            }
        });
    });

    // 操作按钮功能
    const contactButtons = document.querySelectorAll('.btn-contact');
    contactButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('.market-row');
            const productName = row.querySelector('.product-name').textContent;
            const supplierName = row.querySelector('.supplier-name').textContent;
            showContactModal(productName, supplierName);
        });
    });

    const messageButtons = document.querySelectorAll('.btn-message');
    messageButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const row = this.closest('.market-row');
            const productName = row.querySelector('.product-name').textContent;
            const supplierName = row.querySelector('.supplier-name').textContent;
            showMessageModal(productName, supplierName);
        });
    });

    const favoriteButtons = document.querySelectorAll('.btn-favorite');
    favoriteButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            this.classList.toggle('favorited');
            const isFavorited = this.classList.contains('favorited');
            this.style.backgroundColor = isFavorited ? '#dc3545' : '#6c757d';

            const row = this.closest('.market-row');
            const productName = row.querySelector('.product-name').textContent;

            if (isFavorited) {
                showNotification('success', `已收藏 ${productName}`);
            } else {
                showNotification('info', `已取消收藏 ${productName}`);
            }
        });
    });
}

// 显示联系模态框
function showContactModal(productName, supplierName) {
    const modal = createModal('联系供应商', `
        <div class="contact-info">
            <h4>产品：${productName}</h4>
            <h4>供应商：${supplierName}</h4>
            <div class="contact-details">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>电话：0755-88888888</span>
                    <button class="btn btn-sm btn-primary" onclick="callSupplier()">拨打</button>
                </div>
                <div class="contact-item">
                    <i class="fas fa-mobile-alt"></i>
                    <span>手机：138-0000-0000</span>
                    <button class="btn btn-sm btn-primary" onclick="callSupplier()">拨打</button>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span>邮箱：<EMAIL></span>
                    <button class="btn btn-sm btn-secondary" onclick="sendEmail()">发邮件</button>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>地址：深圳市福田区华强北路</span>
                </div>
            </div>
        </div>
        <div class="modal-actions">
            <button class="btn btn-secondary" onclick="closeModal()">关闭</button>
            <button class="btn btn-primary" onclick="startChat()">在线咨询</button>
        </div>
    `);

    document.body.appendChild(modal);
}

// 显示消息模态框
function showMessageModal(productName, supplierName) {
    const modal = createModal('发送消息', `
        <form id="message-form">
            <div class="message-header">
                <h4>发送给：${supplierName}</h4>
                <p>关于：${productName}</p>
            </div>
            <div class="form-group">
                <label>消息内容：</label>
                <textarea name="message" class="form-textarea" rows="6" placeholder="请输入您要发送的消息..." required></textarea>
            </div>
            <div class="form-group">
                <label>联系方式：</label>
                <input type="text" name="contact" class="form-input" placeholder="请输入您的联系方式" required>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="submit" class="btn btn-primary">发送消息</button>
            </div>
        </form>
    `);

    document.body.appendChild(modal);

    // 添加表单提交事件
    const messageForm = document.getElementById('message-form');
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        showNotification('success', '消息发送成功！');
        closeModal();
    });
}

// 显示通知
function showNotification(type, message) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">&times;</button>
    `;

    // 添加通知样式
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                padding: 15px 20px;
                display: flex;
                align-items: center;
                gap: 10px;
                z-index: 10001;
                min-width: 300px;
                animation: slideIn 0.3s ease;
            }
            .notification-success { border-left: 4px solid #28a745; }
            .notification-error { border-left: 4px solid #dc3545; }
            .notification-info { border-left: 4px solid #007bff; }
            .notification i { font-size: 18px; }
            .notification-success i { color: #28a745; }
            .notification-error i { color: #dc3545; }
            .notification-info i { color: #007bff; }
            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                cursor: pointer;
                color: #6c757d;
                margin-left: auto;
            }
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 拨打电话
function callSupplier() {
    showNotification('info', '正在为您拨打电话...');
}

// 发送邮件
function sendEmail() {
    showNotification('info', '正在打开邮件客户端...');
}

// 开始聊天
function startChat() {
    showNotification('success', '正在连接在线客服...');
    closeModal();
}
