@echo off
echo ========================================
echo 大宗交易平台 UI 原型启动器
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo 检测到Python，正在启动服务器...
    echo 服务器地址: http://localhost:8000
    echo 按 Ctrl+C 停止服务器
    echo.
    start http://localhost:8000/demo.html
    python -m http.server 8000
    goto :end
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo 检测到Node.js，正在安装http-server...
    npm install -g http-server
    echo 正在启动服务器...
    echo 服务器地址: http://localhost:8080
    echo 按 Ctrl+C 停止服务器
    echo.
    start http://localhost:8080/demo.html
    http-server -p 8080
    goto :end
)

REM 如果都没有安装，提示用户
echo 未检测到Python或Node.js
echo 请安装以下任一软件后重试:
echo 1. Python 3.x (推荐)
echo 2. Node.js
echo.
echo 或者直接双击 demo.html 文件在浏览器中打开
echo.
pause

:end
