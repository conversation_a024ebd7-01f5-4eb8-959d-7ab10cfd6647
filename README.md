# 大宗交易平台 UI 原型

这是一个基于需求文档创建的大宗交易平台UI原型，使用纯HTML、CSS和JavaScript实现，无需后端支持即可演示所有主要功能界面。

## 功能特性

### 🏠 主要页面
- **工作台** - 数据统计、快捷操作、最新动态
- **货源管理** - 货源发布、列表展示、状态管理
- **交易管理** - 交易统计、需求处理、谈判跟踪
- **订单中心** - 订单列表、状态筛选、详情查看
- **角色工作台** - 谈判人、商务、验货人、监督人专用界面
- **验货管理** - 验货任务、操作界面、历史记录
- **会员中心** - 个人信息、账户管理、认证中心
- **货源市场** - 市场浏览、货源搜索、询价功能

### 🎯 核心功能
- **货源信息发布** - 完整的货源信息录入表单
- **交易需求提交** - 采购需求信息收集
- **合同生成** - 可视化合同编辑界面
- **验货流程** - 验货任务分配和报告提交
- **订单跟踪** - 完整的订单生命周期管理
- **角色权限** - 不同角色的专用工作界面

### 🎨 设计特点
- **响应式设计** - 适配桌面端和移动端
- **现代化界面** - 使用渐变色和卡片式设计
- **交互友好** - 丰富的动画效果和用户反馈
- **信息层次** - 清晰的信息架构和视觉层次
- **状态可视化** - 直观的状态标识和进度展示

## 文件结构

```
ui-prototype/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 交互脚本
└── README.md           # 说明文档
```

## 使用方法

### 1. 直接打开
双击 `index.html` 文件即可在浏览器中打开原型。

### 2. 本地服务器（推荐）
为了获得最佳体验，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用PHP
php -S localhost:8000
```

然后在浏览器中访问 `http://localhost:8000`

## 页面导航

### 顶部导航栏
- **首页** - 返回工作台
- **货源市场** - 浏览所有货源
- **交易中心** - 交易管理页面
- **会员中心** - 个人中心
- **消息通知** - 系统消息（显示未读数量）

### 侧边栏菜单
- **工作台** - 数据概览和快捷操作
- **货源管理** - 货源发布和管理
- **交易管理** - 交易流程管理
- **订单中心** - 订单查看和操作
- **角色工作台** - 角色专用功能
- **验货管理** - 验货任务和报告
- **系统设置** - 系统配置（占位）

## 主要功能演示

### 货源发布流程
1. 点击"发布货源"按钮
2. 填写基本信息（货物名称、类型、数量等）
3. 添加详细描述和图片
4. 填写联系信息
5. 保存草稿或提交审核

### 交易需求提交
1. 点击"提交需求"按钮
2. 填写采购需求信息
3. 填写买方信息
4. 提交需求等待处理

### 订单管理
1. 在订单中心查看所有订单
2. 使用筛选标签过滤订单状态
3. 点击订单查看详细信息
4. 执行相关操作（确认收货、评价等）

### 角色工作台
1. 选择对应的角色标签
2. 查看角色专用的任务列表
3. 处理待办任务
4. 跟踪工作进度

## 技术实现

### HTML结构
- 语义化标签
- 模块化页面结构
- 表单验证属性

### CSS样式
- Flexbox和Grid布局
- CSS变量和渐变
- 响应式媒体查询
- 动画和过渡效果

### JavaScript功能
- 页面路由切换
- 表单验证和提交
- 模态框组件
- 文件上传处理
- 数据模拟和更新

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 自定义和扩展

### 修改样式
编辑 `styles.css` 文件中的CSS变量来快速修改主题色彩：

```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
```

### 添加新页面
1. 在 `index.html` 中添加新的页面div
2. 在 `script.js` 中添加页面切换逻辑
3. 在 `styles.css` 中添加相应样式

### 集成后端API
修改 `script.js` 中的模拟数据函数，替换为实际的API调用。

## 注意事项

1. 这是一个纯前端原型，所有数据都是模拟的
2. 表单提交不会保存到服务器
3. 文件上传功能仅做演示
4. 建议在现代浏览器中查看以获得最佳效果

## 后续开发建议

1. **后端集成** - 连接实际的API接口
2. **数据持久化** - 实现真实的数据存储
3. **用户认证** - 添加登录和权限控制
4. **实时通信** - 集成WebSocket实现实时消息
5. **移动端优化** - 开发专门的移动端应用
6. **性能优化** - 代码分割和懒加载
7. **测试覆盖** - 添加单元测试和集成测试

## 联系信息

如有问题或建议，请联系开发团队。

---

*此原型基于2025年1月的需求文档创建，展示了大宗交易平台的完整业务流程和用户界面设计。*
