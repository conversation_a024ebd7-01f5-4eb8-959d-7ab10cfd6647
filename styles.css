/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
}

.logo i {
    margin-right: 10px;
    font-size: 28px;
}

.nav {
    display: flex;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    padding: 10px 15px;
    border-radius: 5px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

.nav-link .badge {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
}

/* 主要布局 */
.main-container {
    display: flex;
    margin-top: 70px;
    min-height: calc(100vh - 70px);
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    position: fixed;
    left: 0;
    top: 70px;
    bottom: 0;
    overflow-y: auto;
}

.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background-color: #f8f9fa;
    border-left-color: #667eea;
}

.menu-item.active {
    background-color: #e3f2fd;
    border-left-color: #667eea;
    color: #667eea;
}

.menu-item i {
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

/* 主内容区 */
.content {
    flex: 1;
    margin-left: 250px;
    padding: 30px;
    background-color: #f5f7fa;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.page-header h1 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.page-header p {
    color: #7f8c8d;
    font-size: 16px;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 24px;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-info h3 {
    font-size: 28px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 14px;
}

/* 快捷操作 */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h2 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.action-card i {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 15px;
}

.action-card h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.action-card p {
    color: #7f8c8d;
    font-size: 14px;
}

/* 最新动态 */
.recent-activities h2 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.activity-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

.activity-content h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.activity-content p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 5px;
}

.activity-content .time {
    color: #bdc3c7;
    font-size: 12px;
}

/* 筛选条件 */
.filter-bar {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 500;
    color: #2c3e50;
}

.form-select,
.form-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
}

.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: #667eea;
}

/* 货源网格 */
.goods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.goods-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.goods-card:hover {
    transform: translateY(-5px);
}

.goods-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.goods-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    color: white;
}

.status-badge.verified {
    background-color: #27ae60;
}

.status-badge.pending {
    background-color: #f39c12;
}

.goods-info {
    padding: 20px;
}

.goods-info h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 18px;
}

.goods-spec,
.goods-location,
.goods-price {
    margin-bottom: 8px;
    font-size: 14px;
}

.goods-spec {
    color: #7f8c8d;
}

.goods-location {
    color: #95a5a6;
}

.goods-price {
    color: #e74c3c;
    font-weight: bold;
    font-size: 16px;
}

.goods-actions {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

/* 表单样式 */
.form-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.form-section {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
}

.upload-area {
    border: 2px dashed #ddd;
    border-radius: 5px;
    padding: 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.05);
}

.upload-area i {
    font-size: 48px;
    color: #667eea;
    margin-bottom: 10px;
}

/* 交易管理样式 */
.trade-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.trade-stat-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.trade-stat-card h3 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.trade-stat-card p {
    color: #7f8c8d;
    font-size: 14px;
}

.trade-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.trade-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.trade-item:last-child {
    border-bottom: none;
}

.trade-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.trade-info p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 3px;
}

.trade-status {
    margin: 0 20px;
}

.status-badge.negotiating {
    background-color: #f39c12;
}

.status-badge.executing {
    background-color: #3498db;
}

.status-badge.completed {
    background-color: #27ae60;
}

/* 订单中心样式 */
.order-filter {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.filter-tabs {
    display: flex;
    gap: 10px;
}

.filter-tab {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.filter-tab:hover {
    background-color: #f8f9fa;
}

.filter-tab.active {
    background-color: #667eea;
    color: white;
}

.order-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.order-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.order-header {
    background-color: #f8f9fa;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #eee;
}

.order-number {
    font-weight: 500;
    color: #2c3e50;
}

.order-date {
    color: #7f8c8d;
    font-size: 14px;
}

.order-content {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-goods {
    display: flex;
    gap: 15px;
    flex: 2;
}

.order-goods img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 5px;
}

.goods-detail h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.goods-detail p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 3px;
}

.order-parties {
    flex: 2;
}

.party {
    margin-bottom: 10px;
}

.party label {
    font-weight: 500;
    color: #2c3e50;
}

.order-amount {
    flex: 1;
    text-align: right;
}

.amount {
    font-size: 20px;
    font-weight: bold;
    color: #e74c3c;
}

.order-actions {
    padding: 15px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-success {
    background-color: #27ae60;
    color: white;
}

.btn-success:hover {
    background-color: #2ecc71;
}

.btn-warning {
    background-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #f1c40f;
}

/* 角色工作台样式 */
.role-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.role-tab {
    padding: 10px 20px;
    border: none;
    background: white;
    cursor: pointer;
    font-size: 14px;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.role-tab:hover {
    transform: translateY(-2px);
}

.role-tab.active {
    background-color: #667eea;
    color: white;
}

.role-content {
    display: none;
}

.role-content.active {
    display: block;
}

.workspace-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.workspace-stat {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.workspace-stat h3 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.workspace-stat p {
    color: #7f8c8d;
    font-size: 14px;
}

.task-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
}

.task-list h3 {
    margin-bottom: 20px;
    color: #2c3e50;
}

.task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.task-item:last-child {
    border-bottom: none;
}

.task-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.task-info p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 3px;
}

/* 验货管理样式 */
.verification-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.verification-stat {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.verification-stat h3 {
    font-size: 28px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.verification-stat p {
    color: #7f8c8d;
    font-size: 14px;
}

.verification-list {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.verification-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.verification-item:last-child {
    border-bottom: none;
}

.verification-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.verification-info p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 3px;
}

/* 会员中心样式 */
.member-overview {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.member-info {
    display: flex;
    gap: 30px;
}

.member-avatar {
    text-align: center;
}

.member-avatar img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 10px;
    border: 3px solid #667eea;
}

.member-details {
    flex: 1;
}

.member-details h3 {
    font-size: 24px;
    color: #2c3e50;
    margin-bottom: 10px;
}

.member-details p {
    color: #7f8c8d;
    margin-bottom: 5px;
}

.member-badges {
    margin-top: 10px;
    display: flex;
    gap: 10px;
}

.badge {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.badge.verified {
    background-color: #27ae60;
    color: white;
}

.member-stats {
    display: flex;
    gap: 30px;
    margin-left: auto;
}

.member-stats .stat {
    text-align: center;
}

.member-stats .stat h4 {
    font-size: 20px;
    color: #2c3e50;
    margin-bottom: 5px;
}

.member-stats .stat p {
    color: #7f8c8d;
    font-size: 14px;
}

.member-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.member-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 20px;
}

.member-section h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.account-item,
.cert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.account-item:last-child,
.cert-item:last-child {
    border-bottom: none;
}

.account-item .amount {
    font-size: 16px;
    margin-right: 20px;
}

.status {
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.status.verified {
    background-color: #27ae60;
    color: white;
}

/* 货源市场样式 */
.market-filter {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

/* 货源市场表格样式 */
.market-table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.market-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.market-table thead {
    background-color: #f8f9fa;
    border-bottom: 2px solid #e9ecef;
}

.market-table th {
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-right: 1px solid #e9ecef;
}

.market-table th:last-child {
    border-right: none;
}

.market-row {
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.market-row:hover {
    background-color: #f8f9fa;
}

.market-table td {
    padding: 12px 10px;
    vertical-align: middle;
    border-right: 1px solid #e9ecef;
}

.market-table td:last-child {
    border-right: none;
}

/* 产品信息单元格 */
.product-cell {
    padding: 10px !important;
}

.product-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.product-image {
    width: 60px;
    height: 40px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.product-details {
    flex: 1;
}

.product-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
    font-size: 14px;
}

.product-desc {
    color: #6c757d;
    font-size: 12px;
    line-height: 1.3;
}

/* 供应商信息单元格 */
.supplier-cell {
    padding: 10px !important;
}

.supplier-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.supplier-name {
    color: #2c3e50;
    font-weight: 500;
    font-size: 13px;
    line-height: 1.3;
}

.supplier-badge {
    display: flex;
    gap: 5px;
}

.badge-verified {
    background-color: #28a745;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
}

/* 型号单元格 */
.model-cell {
    color: #495057;
    font-weight: 500;
}

/* 数量单元格 */
.quantity-cell {
    text-align: center;
}

.quantity-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.quantity {
    color: #2c3e50;
    font-weight: 600;
    font-size: 14px;
}

.unit {
    color: #6c757d;
    font-size: 11px;
}

/* 价格单元格 */
.price-cell {
    text-align: center;
}

.price-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.price {
    color: #dc3545;
    font-weight: 600;
    font-size: 14px;
}

.price-unit {
    color: #6c757d;
    font-size: 11px;
}

/* 地区单元格 */
.region-cell {
    color: #495057;
    text-align: center;
}

/* 操作单元格 */
.action-cell {
    text-align: center;
    padding: 8px !important;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 8px;
}

.action-buttons button {
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.2s ease;
}

.btn-contact {
    background-color: #28a745;
    color: white;
}

.btn-contact:hover {
    background-color: #218838;
    transform: scale(1.1);
}

.btn-message {
    background-color: #007bff;
    color: white;
}

.btn-message:hover {
    background-color: #0056b3;
    transform: scale(1.1);
}

.btn-favorite {
    background-color: #dc3545;
    color: white;
}

.btn-favorite:hover {
    background-color: #c82333;
    transform: scale(1.1);
}

/* 复选框样式 */
.row-checkbox,
#selectAll {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 联系信息模态框样式 */
.contact-info {
    margin-bottom: 20px;
}

.contact-info h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 16px;
}

.contact-details {
    margin-top: 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item i {
    width: 20px;
    color: #667eea;
    font-size: 16px;
}

.contact-item span {
    flex: 1;
    color: #495057;
    font-size: 14px;
}

.modal-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 消息表单样式 */
.message-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.message-header h4 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 16px;
}

.message-header p {
    color: #6c757d;
    margin: 0;
    font-size: 14px;
}

/* 收藏按钮激活状态 */
.btn-favorite.favorited {
    background-color: #dc3545 !important;
    color: white !important;
}

/* 表格行悬停效果增强 */
.market-row:hover .action-buttons button {
    transform: scale(1.05);
}

/* 响应式表格 */
@media (max-width: 1200px) {
    .market-table {
        font-size: 12px;
    }

    .product-image {
        width: 50px;
        height: 35px;
    }

    .product-name {
        font-size: 13px;
    }

    .product-desc {
        font-size: 11px;
    }

    .supplier-name {
        font-size: 12px;
    }
}

@media (max-width: 768px) {
    .market-table-container {
        overflow-x: auto;
    }

    .market-table {
        min-width: 800px;
    }

    .market-filter {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        margin-bottom: 10px;
    }
}

/* 供应商名称可点击样式 */
.supplier-name.clickable {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
    transition: color 0.2s ease;
}

.supplier-name.clickable:hover {
    color: #0056b3;
}

/* 供应商详情页面样式 */
.supplier-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: -30px -30px 30px -30px;
    padding: 30px;
}

.supplier-banner {
    display: flex;
    align-items: center;
    gap: 30px;
    max-width: 1200px;
    margin: 0 auto;
}

.supplier-logo img {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    border: 2px solid rgba(255,255,255,0.3);
}

.supplier-info {
    flex: 1;
}

.supplier-info h1 {
    font-size: 28px;
    margin-bottom: 8px;
    font-weight: 600;
}

.supplier-slogan {
    font-size: 16px;
    margin-bottom: 15px;
    opacity: 0.9;
}

.supplier-badges {
    display: flex;
    gap: 10px;
}

.supplier-badges .badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
}

.supplier-badges .badge.verified {
    background-color: #28a745;
}

.supplier-badges .badge.premium {
    background-color: #ffc107;
    color: #212529;
}

.supplier-contact {
    display: flex;
    flex-direction: column;
    gap: 15px;
    align-items: flex-end;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.contact-item i {
    width: 16px;
    text-align: center;
}

.contact-buttons {
    display: flex;
    gap: 10px;
}

/* 供应商导航标签 */
.supplier-nav {
    display: flex;
    gap: 5px;
    margin-bottom: 30px;
    border-bottom: 2px solid #e9ecef;
}

.nav-tab {
    padding: 12px 24px;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-tab.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: #f8f9fa;
}

/* 供应商标签内容 */
.supplier-tab-content {
    display: none;
}

.supplier-tab-content.active {
    display: block;
}

/* 供应商介绍 */
.supplier-intro {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 30px;
    display: flex;
    gap: 30px;
    align-items: center;
}

.intro-image img {
    width: 400px;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

.intro-text {
    flex: 1;
}

.intro-text p {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #495057;
}

/* 热销产品排名 */
.hot-products {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
}

.hot-products h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
}

.product-ranking {
    display: flex;
    gap: 20px;
}

.ranking-sidebar {
    width: 150px;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
}

.ranking-category {
    padding: 12px 16px;
    cursor: pointer;
    border-radius: 6px;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
    transition: all 0.2s ease;
}

.ranking-category:hover {
    background-color: #e9ecef;
}

.ranking-category.active {
    background-color: #007bff;
    color: white;
}

.ranking-content {
    flex: 1;
}

.ranking-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.ranking-table thead {
    background-color: #f8f9fa;
}

.ranking-table th {
    padding: 12px 8px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
}

.ranking-tbody {
    display: none;
}

.ranking-tbody.active {
    display: table-row-group;
}

.ranking-table td {
    padding: 10px 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.ranking-table tr:hover {
    background-color: #f8f9fa;
}

/* 小型操作按钮 */
.btn-contact-small,
.btn-message-small {
    width: 24px;
    height: 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    margin-right: 5px;
    transition: all 0.2s ease;
}

.btn-contact-small {
    background-color: #28a745;
    color: white;
}

.btn-contact-small:hover {
    background-color: #218838;
    transform: scale(1.1);
}

.btn-message-small {
    background-color: #007bff;
    color: white;
}

.btn-message-small:hover {
    background-color: #0056b3;
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .content {
        margin-left: 0;
    }

    .header .container {
        padding: 10px 15px;
    }

    .nav {
        display: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .goods-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .member-info {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .member-stats {
        margin-left: 0;
        margin-top: 20px;
    }

    .order-content {
        flex-direction: column;
        align-items: flex-start;
    }

    .order-goods {
        margin-bottom: 15px;
    }

    .order-parties {
        margin-bottom: 15px;
    }
}
